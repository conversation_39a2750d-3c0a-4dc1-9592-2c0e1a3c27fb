# 文字标签功能实现说明

## 概述

本次实现为Earth3D项目添加了文字标签功能，允许在创建PlaneMarker时同时显示文字标签，特别是用于显示攻击来源国家名称。

## 实现的功能

### 1. TextLabel.js - 文字标签组件
- 创建始终面向相机的3D文字标签
- 支持自定义文字内容、颜色、大小、背景
- 支持相对于marker的位置偏移
- 自动处理文字的边框和背景效果
- 支持透明度和渐变效果

### 2. PlaneMarker.js - 集成文字标签
- 修改了 `createStartMarker`、`createEndMarker`、`createCustomMarker` 方法
- 添加了文字标签配置选项：
  - `showLabel`: 是否显示文字标签
  - `labelText`: 标签文字内容
  - `labelOptions`: 标签样式配置
- 新增 `_createMarkerWithLabel` 方法创建包含标记和标签的组

### 3. SpherePoints.js - 数据流处理
- 修改 `generateFlightDataFromNetworkLog` 方法提取 `attackCountry` 信息
- 修改 `createFlightLinesFromData` 方法传递标签配置到FlightLine

### 4. FlightLine.js - 飞线标记集成
- 修改 `createFlightLineWithShader` 方法支持标签选项
- 在创建起点标记时添加攻击国家文字标签

## 使用方法

### 基本用法

```javascript
import PlaneMarker from './PlaneMarker.js';

// 创建带文字标签的起点标记
const startMarker = PlaneMarker.createStartMarker(position, {
  showLabel: true,
  labelText: "美国",
  labelOptions: {
    fontSize: 14,
    color: "#ffffff",
    backgroundColor: "rgba(255, 0, 0, 0.8)",
    borderColor: "#ffffff",
    borderWidth: 1,
    padding: 6,
    offsetY: 1.5, // 标签在标记上方
  }
});

scene.add(startMarker);
```

### 直接使用TextLabel

```javascript
import TextLabel from './TextLabel.js';

// 创建独立的文字标签
const label = TextLabel.createTextLabel(position, {
  text: "攻击来源",
  fontSize: 16,
  color: "#ffffff",
  backgroundColor: "rgba(0, 0, 0, 0.7)",
  offsetY: 1.2
});

scene.add(label);
```

## 配置选项

### TextLabel 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | string | "Label" | 文字内容 |
| fontSize | number | 16 | 字体大小 |
| fontFamily | string | "Arial, sans-serif" | 字体族 |
| color | string | "#ffffff" | 文字颜色 |
| backgroundColor | string | "rgba(0, 0, 0, 0.7)" | 背景颜色 |
| borderColor | string | "#ffffff" | 边框颜色 |
| borderWidth | number | 1 | 边框宽度 |
| padding | number | 8 | 内边距 |
| opacity | number | 1.0 | 透明度 |
| offsetY | number | 1.2 | Y轴偏移（相对于marker） |
| offsetX | number | 0 | X轴偏移 |
| offsetZ | number | 0 | Z轴偏移 |

### PlaneMarker 标签选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showLabel | boolean | false | 是否显示文字标签 |
| labelText | string | "" | 标签文字内容 |
| labelOptions | object | {} | 标签样式配置（参考TextLabel配置） |

## 数据流

1. **WebSocket数据接收**: SpherePoints接收包含`attackCountry`的网络日志数据
2. **数据提取**: `generateFlightDataFromNetworkLog`提取攻击国家信息
3. **飞线创建**: `createFlightLinesFromData`传递标签配置给FlightLine
4. **标记创建**: `createFlightLineWithShader`创建带文字标签的起点标记
5. **标签显示**: PlaneMarker使用TextLabel创建文字标签并添加到场景

## 测试

项目包含一个测试页面 `test_text_label.html`，可以在浏览器中打开测试文字标签功能：

1. 打开 `test_text_label.html`
2. 点击"添加带标签的标记"按钮
3. 观察3D场景中的文字标签效果

## 注意事项

1. **性能考虑**: 文字标签使用Canvas纹理，大量标签可能影响性能
2. **字体支持**: 确保使用的字体在目标环境中可用
3. **文字换行**: 当前实现支持基本的文字换行功能
4. **内存管理**: 使用`TextLabel.disposeTextLabel()`方法清理不需要的标签

## 兼容性

- 兼容现有的PlaneMarker API
- 不影响现有的飞线和标记功能
- 可选择性启用文字标签功能

## 未来改进

1. 支持更多文字样式（粗体、斜体等）
2. 支持文字动画效果
3. 支持多行文字和更复杂的布局
4. 优化大量标签的渲染性能
5. 支持HTML内容渲染
