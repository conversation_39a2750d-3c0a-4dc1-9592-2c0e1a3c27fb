import * as THREE from "three";
import { GUI } from "lil-gui";
import { EARTH_RADIUS } from "../../../../constants";
import { FlightLine } from "./FlightLine.js";
import { usedata } from "../../../../store/data";

let dataStore;

/**
 * 球面点系统管理类
 *
 * 这是一个单例类，负责管理球面坐标系统和飞线效果。
 *
 * 主要职责：
 * 1. 球面坐标系统管理
 *    - 经纬度到3D坐标的转换
 *    - 坐标映射参数配置（偏移、缩放、旋转等）
 *    - 多种坐标转换算法支持
 *
 * 2. THREE.js场景管理
 *    - 管理点群组的创建和销毁
 *    - 与主场景的集成
 *
 * 3. 飞线系统集成
 *    - 初始化和管理FlightLine实例
 *    - 提供演示飞线数据
 *
 * 4. GUI控制界面
 *    - 坐标系统参数的实时调整
 *    - 预设配置的快速应用
 *
 * 使用示例：
 * ```javascript
 * const spherePoints = new SpherePoints(scene, {
 *   onLoad: () => console.log('系统初始化完成'),
 *   enableGUI: true
 * });
 *
 * // 创建飞线
 * spherePoints.createFlightLine(
 *   { lat: 39.9042, lon: 116.4074 }, // 北京
 *   { lat: 35.6762, lon: 139.6503 }  // 东京
 * );
 * ```
 *
 * @class SpherePoints
 * @singleton
 */
class SpherePoints {
  static instance = null;

  constructor(scene, options = {}) {
    // 单例模式检查
    if (SpherePoints.instance) {
      console.warn("SpherePoints is a singleton. Use SpherePoints.getInstance() instead.");
      return SpherePoints.instance;
    }

    // 基础配置
    this.scene = scene;
    this.onLoad = options.onLoad;
    this.renderer = options.renderer;
    this.enableGUI = options.enableGUI ?? true;

    // THREE.js 相关
    this.pointsGroup = null;
    this.gui = null;

    // 飞线管理器
    this.flightLine = null;

    // 飞线管理
    this.activeFlightLines = new Map(); // 存储活跃的飞线，key为坐标字符串，value为飞线数据
    this.maxFlightLines = 5; // 最大飞线数量
    this.flightLineCreationOrder = []; // 记录飞线创建顺序，用于FIFO清理

    // 动画状态管理
    this.isFlightLineAnimationReady = false; // 飞线动画是否已准备好
    this.pendingNetworkData = []; // 等待处理的网络数据队列
    this.sceneInstance = null; // Scene实例引用

    // 坐标映射参数
    this.coordinateConfig = this._createDefaultCoordinateConfig();

    // 设置单例实例
    SpherePoints.instance = this;

    // 暴露调试方法到全局（仅在开发环境）
    if (typeof window !== "undefined") {
      window.debugSpherePoints = this;
    }

    this._initialize();
  }

  /**
   * 创建默认坐标配置
   * @private
   */
  _createDefaultCoordinateConfig() {
    return {
      longitudeOffset: 120,
      latitudeOffset: 1.4,
      longitudeScale: 1.0,
      latitudeScale: 1.0,
      rotationOffset: 0,
      heightOffset: 0,
      realTimeUpdate: true,
      conversionMethod: "current",
    };
  }

  /**
   * 初始化系统
   * @private
   */
  async _initialize() {
    this._createPointsGroup();
    this._initializeFlightLine();
    // this._addDemoFlightLines();
    this._addToScene();
    this._setupDataStoreListener(); // 设置数据监听
    this._setupSceneListener(); // 设置Scene动画监听

    if (this.enableGUI) {
      // this._createCoordinateGUI();
    }

    // 执行加载完成回调
    if (this.onLoad) {
      this.onLoad();
    }
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    return SpherePoints.instance;
  }

  /**
   * 创建或获取单例实例
   */
  static createInstance(scene, options) {
    if (!SpherePoints.instance) {
      new SpherePoints(scene, options);
    }
    return SpherePoints.instance;
  }

  /**
   * 创建点群组
   * @private
   */
  _createPointsGroup() {
    this.pointsGroup = new THREE.Group();
    this.pointsGroup.name = "SpherePoints";
  }

  /**
   * 初始化飞线管理器
   * @private
   */
  _initializeFlightLine() {
    this.flightLine = new FlightLine(this);
  }

  /**
   * 设置数据存储监听器
   * @private
   */
  _setupDataStoreListener() {
    // 获取数据存储实例
    if (!dataStore) {
      dataStore = usedata();
    }

    // 监听WebSocket消息，处理xdrNetworkLog类型数据
    const originalHandleMessage = dataStore.handleWebSocketMessage;
    dataStore.handleWebSocketMessage = (data) => {
      console.log("🚀 ~ SpherePoints ~ data:", data);
      // 调用原始处理方法
      originalHandleMessage.call(dataStore, data);

      // 处理xdrNetworkLog类型数据
      if (data && data.msgType === "xdrNetworkLog") {
        this.handleXdrNetworkLogData(data);
      }
    };

    console.log("SpherePoints: 数据监听器已设置");
  }

  /**
   * 设置Scene动画监听器
   * @private
   */
  _setupSceneListener() {
    // 延迟设置监听器，确保Scene已经初始化
    setTimeout(() => {
      // 尝试获取Scene实例
      let sceneInstance = null;

      // 方法1: 通过全局变量获取
      if (typeof window !== "undefined" && window.debugScene) {
        sceneInstance = window.debugScene;
      }

      // 方法2: 通过Scene类的静态方法获取
      if (!sceneInstance) {
        try {
          // 动态导入Scene类
          import("../Scene/Scene.js")
            .then(({ Scene }) => {
              sceneInstance = Scene.getInstance();
              this._setupSceneEventListeners(sceneInstance);
            })
            .catch((error) => {
              console.warn("SpherePoints: 动态导入Scene失败:", error);
              // 使用轮询方式等待Scene实例
              this._pollForSceneInstance();
            });
          return;
        } catch (error) {
          console.warn("SpherePoints: 无法导入Scene模块:", error);
        }
      }

      if (sceneInstance) {
        this._setupSceneEventListeners(sceneInstance);
      } else {
        // 使用轮询方式等待Scene实例
        this._pollForSceneInstance();
      }
    }, 100);
  }

  /**
   * 轮询等待Scene实例
   * @private
   */
  _pollForSceneInstance() {
    let attempts = 0;
    const maxAttempts = 50; // 最多尝试50次，每次间隔200ms，总共10秒

    const poll = () => {
      attempts++;

      // 尝试从全局获取Scene实例
      let sceneInstance = null;
      if (typeof window !== "undefined" && window.debugScene) {
        sceneInstance = window.debugScene;
      }

      if (sceneInstance) {
        console.log(`SpherePoints: 在第${attempts}次尝试中找到Scene实例`);
        this._setupSceneEventListeners(sceneInstance);
        return;
      }

      if (attempts < maxAttempts) {
        setTimeout(poll, 200);
      } else {
        console.warn("SpherePoints: 超时未找到Scene实例，将使用备用方案");
        // 备用方案：直接设置为准备状态
        this.isFlightLineAnimationReady = true;
      }
    };

    poll();
  }

  /**
   * 设置Scene事件监听器
   * @private
   */
  _setupSceneEventListeners(sceneInstance) {
    if (!sceneInstance) {
      console.warn("SpherePoints: Scene实例为空，无法设置监听器");
      return;
    }

    this.sceneInstance = sceneInstance;

    // 监听地球动画更新事件
    sceneInstance.addEventListener("earthAnimationUpdate", (data) => {
      this._handleEarthAnimationUpdate(data);
    });

    // 监听飞线动画触发事件
    sceneInstance.addEventListener("flightLineAnimationsStarted", () => {
      this.isFlightLineAnimationReady = true;
      console.log("SpherePoints: 飞线动画已启动，开始处理待处理的网络数据");
      this._processPendingNetworkData();
    });

    console.log("SpherePoints: Scene动画监听器已设置");
  }

  /**
   * 处理地球动画更新事件
   * @private
   */
  _handleEarthAnimationUpdate(data) {
    // 检查飞线动画是否已触发
    if (data.flightLineAnimationTriggered && !this.isFlightLineAnimationReady) {
      this.isFlightLineAnimationReady = true;
      console.log("SpherePoints: 飞线动画已触发，开始处理待处理的网络数据");
      this._processPendingNetworkData();
    }
  }

  /**
   * 处理待处理的网络数据
   * @private
   */
  _processPendingNetworkData() {
    if (this.pendingNetworkData.length === 0) {
      console.log("SpherePoints: 没有待处理的网络数据");
      return;
    }

    console.log(`SpherePoints: 开始处理 ${this.pendingNetworkData.length} 条待处理的网络数据`);

    // 处理所有待处理的数据
    const dataToProcess = [...this.pendingNetworkData];
    this.pendingNetworkData.length = 0; // 清空待处理队列

    dataToProcess.forEach((data, index) => {
      console.log(`SpherePoints: 处理待处理数据 ${index + 1}/${dataToProcess.length}`);
      this._processXdrNetworkLogDataImmediate(data);
    });

    console.log("SpherePoints: 所有待处理的网络数据已处理完成");
  }

  /**
   * 添加到场景
   * @private
   */
  _addToScene() {
    if (this.scene && this.pointsGroup) {
      this.scene.add(this.pointsGroup);
    }
  }

  /**
   * 将经纬度坐标转换为球面上的3D坐标
   *
   * 这是核心的坐标转换方法，支持多种转换算法和参数调整。
   * 转换过程包括：
   * 1. 应用坐标配置调整（偏移、缩放）
   * 2. 度数转弧度
   * 3. 根据选择的算法进行球面坐标转换
   *
   * 支持的转换算法：
   * - current: 当前算法（修正经度方向）
   * - standard: 标准球面坐标转换
   * - negLon: 经度取负值
   * - offset90: 经度偏移90度
   * - negOffset90: 经度取负值并偏移90度
   *
   * @param {number} latitude - 纬度，范围 -90 到 90 度
   * @param {number} longitude - 经度，范围 -180 到 180 度
   * @param {number} [radius=EARTH_RADIUS] - 球体半径，默认为地球半径
   * @returns {THREE.Vector3} 转换后的3D坐标向量
   *
   * @example
   * // 北京坐标转换
   * const beijingPos = spherePoints.latLonToVector3(39.9042, 116.4074);
   * console.log(beijingPos); // Vector3 { x: ..., y: ..., z: ... }
   */
  latLonToVector3(latitude, longitude, radius = EARTH_RADIUS) {
    // 应用坐标配置调整
    const config = this.coordinateConfig;
    const adjustedLat = (latitude + config.latitudeOffset) * config.latitudeScale;
    const adjustedLon = (longitude + config.longitudeOffset) * config.longitudeScale;
    const adjustedRadius = radius + config.heightOffset;

    // 将度转换为弧度
    const lat = (adjustedLat * Math.PI) / 180;
    const lon = (adjustedLon * Math.PI) / 180;
    const rotOffset = (config.rotationOffset * Math.PI) / 180;

    let x, y, z;

    // 根据选择的转换方法计算坐标
    switch (config.conversionMethod) {
      case "standard":
        // 标准球面坐标转换
        x = adjustedRadius * Math.cos(lat) * Math.cos(lon + rotOffset);
        y = adjustedRadius * Math.sin(lat);
        z = adjustedRadius * Math.cos(lat) * Math.sin(lon + rotOffset);
        break;

      case "negLon":
        // 经度取负值
        x = adjustedRadius * Math.cos(lat) * Math.cos(-lon + rotOffset);
        y = adjustedRadius * Math.sin(lat);
        z = adjustedRadius * Math.cos(lat) * Math.sin(-lon + rotOffset);
        break;

      case "offset90":
        // 经度偏移90度
        x = adjustedRadius * Math.cos(lat) * Math.cos(lon + Math.PI / 2 + rotOffset);
        y = adjustedRadius * Math.sin(lat);
        z = adjustedRadius * Math.cos(lat) * Math.sin(lon + Math.PI / 2 + rotOffset);
        break;

      case "negOffset90":
        // 经度取负值并偏移90度
        x = adjustedRadius * Math.cos(lat) * Math.cos(-lon + Math.PI / 2 + rotOffset);
        y = adjustedRadius * Math.sin(lat);
        z = adjustedRadius * Math.cos(lat) * Math.sin(-lon + Math.PI / 2 + rotOffset);
        break;

      default:
      case "current":
        // 当前算法（修正经度方向）
        const adjustedLonRad = (-(adjustedLon - 90) * Math.PI) / 180;
        x = adjustedRadius * Math.cos(lat) * Math.cos(adjustedLonRad + rotOffset);
        y = adjustedRadius * Math.sin(lat);
        z = adjustedRadius * Math.cos(lat) * Math.sin(adjustedLonRad + rotOffset);
        break;
    }

    return new THREE.Vector3(x, y, z);
  }

  /**
   * 添加演示飞线（从北京到世界各大城市）
   * @private
   */
  _addDemoFlightLines() {
    if (!this.flightLine) {
      console.warn("FlightLine未初始化，无法创建飞线");
      return;
    }

    const demoData = this._getDemoFlightData();

    demoData.routes.forEach((route, index) => {
      // 调试：输出坐标转换信息
      const originPos = this.latLonToVector3(demoData.origin.lat, demoData.origin.lon);
      const destPos = this.latLonToVector3(route.destination.lat, route.destination.lon);

      console.log(`=== ${route.destination.name} 坐标转换信息 ===`);
      console.log(`原始坐标: 纬度${route.destination.lat}°, 经度${route.destination.lon}°`);
      console.log(`3D坐标: (${destPos.x.toFixed(2)}, ${destPos.y.toFixed(2)}, ${destPos.z.toFixed(2)})`);
      console.log(`${demoData.origin.name}3D坐标: (${originPos.x.toFixed(2)}, ${originPos.y.toFixed(2)}, ${originPos.z.toFixed(2)})`);
      console.log(`距离: ${originPos.distanceTo(destPos).toFixed(2)}`);

      this.createFlightLine(demoData.origin, route.destination, {
        pointOptions: {
          color: route.color,
          size: 2.0,
          opacity: 0.9,
        },
        lineOptions: {
          color: route.color,
          opacity: 0.8,
          segments: 60,
          height: 8 + index * 3,
        },
        animationOptions: {
          duration: 4000 + index * 500,
        },
      });

      console.log(`创建演示飞线: ${demoData.origin.name} -> ${route.destination.name}`);
    });
  }

  /**
   * 获取演示飞线数据
   * @private
   */
  _getDemoFlightData() {
    return {
      origin: { name: "北京", lat: 39.9042, lon: 116.4074 },
      routes: [
        { destination: { name: "东京", lat: 35.6762, lon: 139.6503 }, color: 0x00ffff },
        { destination: { name: "纽约", lat: 40.7128, lon: -74.006 + 360 }, color: 0xff6b6b },
        { destination: { name: "伦敦", lat: 51.5074, lon: -0.1278 }, color: 0x4ecdc4 },
        { destination: { name: "悉尼", lat: -33.8688, lon: 151.2093 }, color: 0xffe66d },
        { destination: { name: "巴黎", lat: 48.8566, lon: 2.3522 }, color: 0xa8e6cf },
      ],
    };
  }

  /**
   * 处理xdrNetworkLog类型数据
   * @param {Object} data - WebSocket接收到的数据
   */
  handleXdrNetworkLogData(data) {
    try {
      console.log("SpherePoints: 接收到xdrNetworkLog数据:", data);

      if (!data.msgContent || !Array.isArray(data.msgContent)) {
        console.warn("SpherePoints: msgContent不是数组或为空");
        return;
      }

      // 检查飞线动画是否已准备好
      if (!this.isFlightLineAnimationReady) {
        console.log("SpherePoints: 飞线动画尚未准备好，将数据加入待处理队列");
        this.pendingNetworkData.push(data);
        console.log(`SpherePoints: 待处理队列长度: ${this.pendingNetworkData.length}`);
        return;
      }

      // 飞线动画已准备好，立即处理数据
      this._processXdrNetworkLogDataImmediate(data);
    } catch (error) {
      console.error("SpherePoints: 处理xdrNetworkLog数据失败:", error);
    }
  }

  /**
   * 立即处理xdrNetworkLog数据（内部方法）
   * @param {Object} data - WebSocket接收到的数据
   * @private
   */
  _processXdrNetworkLogDataImmediate(data) {
    try {
      console.log("SpherePoints: 立即处理xdrNetworkLog数据:", data);

      // 处理msgContent数组中的每个数据项
      data.msgContent.forEach((item, index) => {
        try {
          this.processNetworkLogItem(item, index);
        } catch (itemError) {
          console.error(`SpherePoints: 处理第${index + 1}个数据项失败:`, itemError, item);
        }
      });
    } catch (error) {
      console.error("SpherePoints: 立即处理xdrNetworkLog数据失败:", error);
    }
  }

  /**
   * 处理单个网络日志数据项
   * @param {Object} item - 单个数据项
   * @param {number} index - 数据项索引
   */
  processNetworkLogItem(item, index) {
    console.log("🚀 ~ SpherePoints ~ item, index:", item, index);
    // 检查是否包含startArray和endArray
    if (!item.startArray || !item.endArray) {
      console.warn(`SpherePoints: 第${index + 1}个数据项缺少startArray或endArray:`, item);
      return;
    }

    // 生成飞线数据并创建飞线
    const flightData = this.generateFlightDataFromNetworkLog(item, index);
    console.log("🚀 ~ SpherePoints ~ flightData:", flightData);
    if (flightData) {
      // 将 cardInfo 传递给飞线创建方法
      flightData.cardInfo = item.cardInfo;
      flightData.startArray = item.startArray;
      flightData.endArray = item.endArray;
      this.createFlightLinesFromData(flightData);
    }
  }

  /**
   * 生成飞线的唯一标识符
   * @param {number} startLat - 起点纬度
   * @param {number} startLon - 起点经度
   * @param {number} endLat - 终点纬度
   * @param {number} endLon - 终点经度
   * @returns {string} - 飞线唯一标识符
   */
  generateFlightLineKey(startLat, startLon, endLat, endLon) {
    // 将坐标保留到小数点后4位，避免微小差异导致的重复
    const start = `${startLat.toFixed(4)},${startLon.toFixed(4)}`;
    const end = `${endLat.toFixed(4)},${endLon.toFixed(4)}`;
    return `${start}->${end}`;
  }

  /**
   * 检查飞线是否已存在
   * @param {number} startLat - 起点纬度
   * @param {number} startLon - 起点经度
   * @param {number} endLat - 终点纬度
   * @param {number} endLon - 终点经度
   * @returns {boolean} - 是否已存在
   */
  isFlightLineExists(startLat, startLon, endLat, endLon) {
    const key = this.generateFlightLineKey(startLat, startLon, endLat, endLon);
    return this.activeFlightLines.has(key);
  }

  /**
   * 清理最旧的飞线以腾出空间
   * @param {number} count - 需要清理的数量，默认为1
   */
  removeOldestFlightLines(count = 1) {
    console.log(`🗑️ 开始清理 ${count} 条最旧的飞线，当前总数: ${this.activeFlightLines.size}`);

    for (let i = 0; i < count && this.flightLineCreationOrder.length > 0; i++) {
      const oldestKey = this.flightLineCreationOrder.shift();
      const flightLineData = this.activeFlightLines.get(oldestKey);

      if (flightLineData) {
        console.log(`🗑️ 清理最旧的飞线: ${oldestKey}`);

        // 停止飞线动画
        if (this.flightLine && this.flightLine.stopFlightLineAnimation) {
          this.flightLine.stopFlightLineAnimation(flightLineData);
        }

        // 从场景中移除飞线相关对象
        this.removeFlightLineFromScene(flightLineData);

        // 从管理器中移除
        this.activeFlightLines.delete(oldestKey);

        console.log(`✅ 飞线已清理: ${oldestKey}`);
      } else {
        console.warn(`⚠️ 未找到飞线数据: ${oldestKey}`);
      }
    }

    console.log(`📊 清理完成，剩余飞线: ${this.activeFlightLines.size}/${this.maxFlightLines}`);
    console.log(`📋 剩余创建顺序: [${this.flightLineCreationOrder.join(", ")}]`);
  }

  /**
   * 从场景中移除飞线对象
   * @param {Object} flightLineData - 飞线数据对象
   */
  removeFlightLineFromScene(flightLineData) {
    try {
      // 移除起点和终点标识
      if (flightLineData.startPoint && flightLineData.startPoint.parent) {
        flightLineData.startPoint.parent.remove(flightLineData.startPoint);
      }
      if (flightLineData.endPoint && flightLineData.endPoint.parent) {
        flightLineData.endPoint.parent.remove(flightLineData.endPoint);
      }

      // 移除飞线
      if (flightLineData.line && flightLineData.line.parent) {
        flightLineData.line.parent.remove(flightLineData.line);

        // 销毁飞线的几何体和材质
        if (flightLineData.line.geometry) {
          flightLineData.line.geometry.dispose();
        }
        if (flightLineData.line.material) {
          flightLineData.line.material.dispose();
        }
      }

      // 移除粒子
      if (flightLineData.particles && Array.isArray(flightLineData.particles)) {
        flightLineData.particles.forEach((particle) => {
          // 处理不同的粒子结构
          if (particle && particle.point && particle.point.parent) {
            // 新的shader粒子系统结构：{ point, material, geometry }
            particle.point.parent.remove(particle.point);

            // 销毁几何体和材质以释放内存
            if (particle.geometry) {
              particle.geometry.dispose();
            }
            if (particle.material) {
              particle.material.dispose();
            }

            console.log(`✅ 粒子点已从场景中移除: ${particle.point.name || "unnamed"}`);
          } else if (particle && particle.parent) {
            // 旧的粒子结构：直接是THREE.Object3D
            particle.parent.remove(particle);

            // 销毁几何体和材质
            if (particle.geometry) {
              particle.geometry.dispose();
            }
            if (particle.material) {
              particle.material.dispose();
            }

            console.log(`✅ 粒子已从场景中移除: ${particle.name || "unnamed"}`);
          } else {
            console.warn(`⚠️ 无法移除粒子，结构不匹配:`, particle);
          }
        });
      }

      // 从 FlightLine 管理器的数组中移除飞线
      if (this.flightLine && this.flightLine.flightLines) {
        const index = this.flightLine.flightLines.findIndex((flight) => flight.id === flightLineData.id);
        if (index > -1) {
          this.flightLine.flightLines.splice(index, 1);
          console.log(`✅ 飞线已从 FlightLine 数组中移除: ${flightLineData.id}`);
        }
      }

      console.log(`🗑️ 飞线对象已从场景中移除: ${flightLineData.id}`);
    } catch (error) {
      console.error("移除飞线对象时出错:", error);
    }
  }

  /**
   * 添加飞线到管理器
   * @param {Object} flightLineData - 飞线数据对象
   * @param {number} startLat - 起点纬度
   * @param {number} startLon - 起点经度
   * @param {number} endLat - 终点纬度
   * @param {number} endLon - 终点经度
   */
  addFlightLineToManager(flightLineData, startLat, startLon, endLat, endLon) {
    const key = this.generateFlightLineKey(startLat, startLon, endLat, endLon);

    // 添加到管理器
    this.activeFlightLines.set(key, flightLineData);
    this.flightLineCreationOrder.push(key);

    console.log(`✅ 飞线已添加到管理器: ${key}`);
    console.log(`📊 当前飞线统计: ${this.activeFlightLines.size}/${this.maxFlightLines}`);
    console.log(`📋 创建顺序: [${this.flightLineCreationOrder.join(", ")}]`);
  }

  /**
   * 从网络日志数据生成飞线数据
   * @param {Object} item - 网络日志数据项
   * @param {number} index - 数据项索引
   * @returns {Object|null} - 类似_getDemoFlightData的数据结构
   */
  generateFlightDataFromNetworkLog(item, index) {
    try {
      // 提取起始点坐标（startArray是单个对象）
      const origin = this.extractCoordinateFromObject(item.startArray, "start", index);
      if (!origin) {
        return null;
      }

      // 提取终点坐标（endArray是数组）
      const routes = [];
      if (Array.isArray(item.endArray)) {
        item.endArray.forEach((endPoint, endIndex) => {
          const destination = this.extractCoordinateFromObject(endPoint, "end", `${index}-${endIndex}`);
          if (destination) {
            // 统一使用青色 0x00ffff
            const color = 0x00ffff;

            routes.push({
              destination: destination,
              color: color,
            });
          }
        });
      }

      if (routes.length === 0) {
        console.warn(`SpherePoints: 第${index + 1}个数据项没有有效的终点坐标`);
        return null;
      }

      return {
        origin: origin,
        routes: routes,
        // 添加攻击国家信息用于文字标签
        attackCountry: item.attackCountry || "",
      };
    } catch (error) {
      console.error(`SpherePoints: 生成第${index + 1}个数据项的飞线数据失败:`, error);
      return null;
    }
  }

  /**
   * 从坐标对象中提取经纬度
   * @param {Object} coordObj - 坐标对象 {E: 经度, N: 纬度, name: 名称}
   * @param {string} type - 坐标类型 ('start' 或 'end')
   * @param {string|number} identifier - 标识符
   * @returns {Object|null} - {name: string, lat: number, lon: number} 或 null
   */
  extractCoordinateFromObject(coordObj, type, identifier) {
    if (!coordObj || typeof coordObj !== "object") {
      console.warn(`SpherePoints: ${identifier}的${type}坐标对象格式不正确:`, coordObj);
      return null;
    }

    // 提取经纬度，支持多种字段名
    const lon = parseFloat(coordObj.E || coordObj.longitude || coordObj.lon || coordObj.lng);
    const lat = parseFloat(coordObj.N || coordObj.latitude || coordObj.lat);
    const name = coordObj.name || `${type}_${identifier}`;

    // 验证坐标范围
    if (isNaN(lat) || isNaN(lon) || lat < -90 || lat > 90 || lon < -180 || lon > 180) {
      console.warn(`SpherePoints: ${identifier}的${type}坐标无效: lat=${lat}, lon=${lon}`);
      return null;
    }

    return { name, lat, lon };
  }

  /**
   * 根据飞线数据创建飞线（类似_addDemoFlightLines的逻辑）
   * @param {Object} flightData - 飞线数据 {origin: {name, lat, lon}, routes: [{destination: {name, lat, lon}, color}]}
   */
  createFlightLinesFromData(flightData) {
    if (!this.flightLine) {
      console.warn("FlightLine未初始化，无法创建飞线");
      return;
    }

    const { origin, routes } = flightData;
    let createdCount = 0;

    routes.forEach((route, index) => {
      // 检查飞线是否已存在
      if (this.isFlightLineExists(origin.lat, origin.lon, route.destination.lat, route.destination.lon)) {
        console.log(`飞线已存在，跳过创建: ${origin.name} -> ${route.destination.name}`);
        return;
      }

      // 检查是否需要清理旧飞线以腾出空间
      // 确保在创建新飞线前，活跃飞线数量不超过限制
      while (this.activeFlightLines.size >= this.maxFlightLines) {
        console.log(`飞线数量已达上限(${this.maxFlightLines})，清理1条最旧的飞线`);
        this.removeOldestFlightLines(1);
      }

      // 调试：输出坐标转换信息
      const originPos = this.latLonToVector3(origin.lat, origin.lon);
      const destPos = this.latLonToVector3(route.destination.lat, route.destination.lon);

      console.log(`=== ${route.destination.name} 坐标转换信息 ===`);
      console.log(`原始坐标: 纬度${route.destination.lat}°, 经度${route.destination.lon}°`);
      console.log(`3D坐标: (${destPos.x.toFixed(2)}, ${destPos.y.toFixed(2)}, ${destPos.z.toFixed(2)})`);
      console.log(`${origin.name}3D坐标: (${originPos.x.toFixed(2)}, ${originPos.y.toFixed(2)}, ${originPos.z.toFixed(2)})`);
      console.log(`距离: ${originPos.distanceTo(destPos).toFixed(2)}`);

      // 创建飞线
      const flightLineData = this.flightLine.createFlightLineWithShader(
        origin.lat,
        origin.lon,
        route.destination.lat,
        route.destination.lon,
        {
          pointOptions: {
            color: route.color,
            size: 2.0,
            opacity: 0.9,
          },
          lineOptions: {
            color: route.color,
            opacity: 0.8,
            segments: 60,
            height: 8 + index * 3,
          },
          animationOptions: {
            duration: 4000 + index * 500,
          },
          // 添加文字标签选项
          labelOptions: {
            showLabel: true, // 启用文字标签
            attackCountry: flightData.attackCountry, // 传递攻击国家信息
          },
        },
        flightData.cardInfo,
        flightData.startArray,
        flightData.endArray
      );

      // 如果飞线创建成功，添加到管理器并启动动画
      if (flightLineData) {
        // 添加到飞线管理器
        this.addFlightLineToManager(flightLineData, origin.lat, origin.lon, route.destination.lat, route.destination.lon);

        console.log(`立即启动飞线动画: ${origin.name} -> ${route.destination.name}`);

        // 添加延迟让飞线依次出现
        setTimeout(() => {
          this.flightLine.startFlightLineAnimation(flightLineData, {
            drawDuration: 1000, // 线条绘制时间
            flowDelay: 100, // 流光动画延迟
          });
        }, createdCount * 300); // 每条飞线延迟300ms

        createdCount++;
        console.log(`创建网络飞线: ${origin.name} -> ${route.destination.name} (${createdCount}/${routes.length})`);
      } else {
        console.warn(`飞线创建失败: ${origin.name} -> ${route.destination.name}`);
      }
    });

    console.log(`飞线创建完成，本次创建: ${createdCount}条，当前总数: ${this.activeFlightLines.size}/${this.maxFlightLines}`);

    // 输出详细统计信息用于调试
    const stats = this.getFlightLineStats();
    console.log(`📊 飞线统计信息:`, stats);
    if (stats.mismatch) {
      console.warn(`⚠️ 检测到飞线数量不一致! SpherePoints: ${stats.total}, FlightLine: ${stats.flightLineArrayCount}`);
    }
  }

  /**
   * 创建飞线
   * @param {Object} startPoint - 起始点 {lat: number, lon: number}
   * @param {Object} endPoint - 终点 {lat: number, lon: number}
   */
  createFlightLine(startPoint, endPoint) {
    console.log(`创建飞线: (${startPoint.lat}, ${startPoint.lon}) -> (${endPoint.lat}, ${endPoint.lon})`);

    if (!this.flightLine) {
      console.warn("FlightLine未初始化");
      return null;
    }

    return this.flightLine.createFlightLineWithShader(startPoint.lat, startPoint.lon, endPoint.lat, endPoint.lon, {
      pointOptions: {
        color: 0x00ffff,
        size: 2.0,
        opacity: 0.9,
      },
      lineOptions: {
        color: 0x00ffff,
        opacity: 0.8,
        segments: 60,
        height: 6,
      },
      animationOptions: {
        duration: 4000,
      },
    });
  }

  /**
   * 获取当前活跃飞线的统计信息
   * @returns {Object} - 飞线统计信息
   */
  getFlightLineStats() {
    const flightLineArrayCount = this.flightLine ? this.flightLine.flightLines.length : 0;

    return {
      total: this.activeFlightLines.size,
      max: this.maxFlightLines,
      remaining: this.maxFlightLines - this.activeFlightLines.size,
      keys: Array.from(this.activeFlightLines.keys()),
      creationOrder: [...this.flightLineCreationOrder],
      flightLineArrayCount: flightLineArrayCount, // FlightLine 数组中的数量
      mismatch: this.activeFlightLines.size !== flightLineArrayCount, // 是否存在不一致
    };
  }

  /**
   * 清理所有飞线
   */
  clearAllFlightLines() {
    console.log(`清理所有飞线，当前数量: ${this.activeFlightLines.size}`);

    // 停止并移除所有飞线
    this.activeFlightLines.forEach((flightLineData, key) => {
      // 停止飞线动画
      if (this.flightLine && this.flightLine.stopFlightLineAnimation) {
        this.flightLine.stopFlightLineAnimation(flightLineData);
      }

      // 从场景中移除飞线相关对象
      this.removeFlightLineFromScene(flightLineData);
    });

    // 清空管理器
    this.activeFlightLines.clear();
    this.flightLineCreationOrder.length = 0;

    console.log("所有飞线已清理完成");
  }

  /**
   * 设置最大飞线数量
   * @param {number} maxCount - 最大数量
   */
  setMaxFlightLines(maxCount) {
    if (maxCount < 1) {
      console.warn("最大飞线数量不能小于1");
      return;
    }

    const oldMax = this.maxFlightLines;
    this.maxFlightLines = maxCount;

    console.log(`飞线数量限制已更新: ${oldMax} -> ${maxCount}`);

    // 如果当前飞线数量超过新的限制，清理多余的
    if (this.activeFlightLines.size > maxCount) {
      const needToRemove = this.activeFlightLines.size - maxCount;
      console.log(`当前飞线数量(${this.activeFlightLines.size})超过新限制(${maxCount})，清理${needToRemove}条最旧的飞线`);
      this.removeOldestFlightLines(needToRemove);
    }
  }

  /**
   * 调试方法：强制检查并修复飞线数量
   */
  debugCheckAndFixFlightLines() {
    const stats = this.getFlightLineStats();
    console.log(`🔍 调试检查飞线状态:`, stats);

    if (stats.mismatch) {
      console.warn(`⚠️ 发现飞线数量不一致，正在修复...`);

      // 如果 SpherePoints 的数量超过限制，清理多余的
      while (this.activeFlightLines.size > this.maxFlightLines) {
        console.log(`🔧 修复: 清理超出限制的飞线`);
        this.removeOldestFlightLines(1);
      }
    }

    const finalStats = this.getFlightLineStats();
    console.log(`✅ 修复完成:`, finalStats);
    return finalStats;
  }

  addToScene() {
    if (this.scene && this.pointsGroup) {
      this.scene.add(this.pointsGroup);
    }
  }

  /**
   * 创建坐标系统GUI控制面板
   * @private
   */
  _createCoordinateGUI() {
    this.gui = new GUI({ title: "球面坐标系统控制" });

    // 坐标偏移控制
    const offsetFolder = this.gui.addFolder("坐标偏移");
    offsetFolder
      .add(this.coordinateConfig, "longitudeOffset", -180, 180, 0.1)
      .name("经度偏移 (°)")
      .onChange(() => this._updateAllPoints());

    offsetFolder
      .add(this.coordinateConfig, "latitudeOffset", -90, 90, 0.1)
      .name("纬度偏移 (°)")
      .onChange(() => this._updateAllPoints());

    offsetFolder
      .add(this.coordinateConfig, "rotationOffset", -180, 180, 0.1)
      .name("旋转偏移 (°)")
      .onChange(() => this._updateAllPoints());

    offsetFolder
      .add(this.coordinateConfig, "heightOffset", -5, 5, 0.1)
      .name("高度偏移")
      .onChange(() => this._updateAllPoints());

    // 缩放控制
    const scaleFolder = this.gui.addFolder("坐标缩放");
    scaleFolder
      .add(this.coordinateConfig, "longitudeScale", 0.1, 2.0, 0.01)
      .name("经度缩放")
      .onChange(() => this._updateAllPoints());

    scaleFolder
      .add(this.coordinateConfig, "latitudeScale", 0.1, 2.0, 0.01)
      .name("纬度缩放")
      .onChange(() => this._updateAllPoints());

    // 控制选项
    const controlFolder = this.gui.addFolder("控制选项");
    controlFolder.add(this.coordinateConfig, "realTimeUpdate").name("实时更新");

    controlFolder
      .add(this.coordinateConfig, "conversionMethod", ["current", "standard", "negLon", "offset90", "negOffset90"])
      .name("转换算法")
      .onChange(() => this._updateAllPoints());

    controlFolder.add(this, "_resetCoordinateConfig").name("重置坐标配置");
    controlFolder.add(this, "_updateAllPoints").name("手动更新");

    // 预设选项
    const presetFolder = this.gui.addFolder("预设调整");
    presetFolder.add(this, "_applyCoordinatePreset", ["默认", "中国优化", "欧洲优化", "美洲优化"]).name("应用预设");

    // 默认展开主要控制面板
    offsetFolder.open();
    scaleFolder.open();
  }

  /**
   * 更新所有点的位置
   * @private
   */
  _updateAllPoints() {
    if (!this.coordinateConfig.realTimeUpdate) return;

    // 委托给FlightLine更新点的位置
    if (this.flightLine) {
      this.flightLine.updateAllPoints();
    }
  }

  /**
   * 重置坐标配置
   * @private
   */
  _resetCoordinateConfig() {
    Object.assign(this.coordinateConfig, this._createDefaultCoordinateConfig());

    // 更新GUI显示
    if (this.gui) {
      this.gui.updateDisplay();
    }

    this._updateAllPoints();
    console.log("坐标配置已重置");
  }

  /**
   * 应用坐标预设
   * @private
   */
  _applyCoordinatePreset(presetName) {
    const presets = {
      默认: this._createDefaultCoordinateConfig(),
      中国优化: {
        ...this._createDefaultCoordinateConfig(),
        heightOffset: 0.1,
      },
      欧洲优化: {
        ...this._createDefaultCoordinateConfig(),
        longitudeOffset: 10,
        latitudeOffset: 5,
        longitudeScale: 1.1,
      },
      美洲优化: {
        ...this._createDefaultCoordinateConfig(),
        longitudeOffset: -100,
        rotationOffset: 90,
      },
    };

    const preset = presets[presetName];
    if (preset) {
      Object.assign(this.coordinateConfig, preset);

      // 更新GUI显示
      if (this.gui) {
        this.gui.updateDisplay();
      }

      this._updateAllPoints();
      console.log(`已应用坐标预设: ${presetName}`);
    }
  }

  // === 公共API方法 ===

  /**
   * 获取当前坐标配置
   */
  getCoordinateConfig() {
    return { ...this.coordinateConfig };
  }

  /**
   * 设置坐标配置
   */
  setCoordinateConfig(config) {
    Object.assign(this.coordinateConfig, config);

    // 更新GUI显示
    if (this.gui) {
      this.gui.updateDisplay();
    }

    this._updateAllPoints();
  }

  /**
   * 获取飞线管理器
   */
  getFlightLineManager() {
    return this.flightLine;
  }

  /**
   * 销毁系统，清理所有资源
   */
  destroy() {
    // 清理飞线管理器
    if (this.flightLine) {
      this.flightLine.destroy();
      this.flightLine = null;
    }

    // 从场景中移除点群组
    if (this.scene && this.pointsGroup) {
      this.scene.remove(this.pointsGroup);
    }

    // 清理GUI
    if (this.gui) {
      this.gui.destroy();
      this.gui = null;
    }

    // 清理引用
    this.pointsGroup = null;
    this.scene = null;
    this.renderer = null;

    // 清除单例实例
    SpherePoints.instance = null;

    console.log("SpherePoints系统已销毁");
  }
}

// Export both named and default exports for flexibility
export { SpherePoints };
export default SpherePoints;
