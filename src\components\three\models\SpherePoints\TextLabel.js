import * as THREE from "three";

/**
 * 3D文字标签工具类
 * 
 * 用于在3D场景中创建始终面向相机的文字标签。
 * 支持自定义文字内容、颜色、大小和位置偏移。
 * 
 * 功能特性：
 * - 文字始终面向相机（Billboard效果）
 * - 支持自定义字体、颜色、大小
 * - 可设置相对于marker的位置偏移
 * - 自动处理文字的边框和背景
 * - 支持透明度和渐变效果
 * 
 * 使用示例：
 * ```javascript
 * import TextLabel from './TextLabel.js';
 * 
 * // 创建文字标签
 * const label = TextLabel.createTextLabel(position, {
 *   text: "美国",
 *   fontSize: 16,
 *   color: "#ffffff",
 *   backgroundColor: "rgba(0,0,0,0.7)"
 * });
 * scene.add(label);
 * ```
 */
class TextLabel {
  /**
   * 创建文字标签
   * @param {THREE.Vector3} position - 标签位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Sprite} 文字标签精灵对象
   */
  static createTextLabel(position, options = {}) {
    const defaultOptions = {
      text: "Label",
      fontSize: 16,
      fontFamily: "Arial, sans-serif",
      color: "#ffffff",
      backgroundColor: "rgba(0, 0, 0, 0.7)",
      borderColor: "#ffffff",
      borderWidth: 1,
      padding: 8,
      opacity: 1.0,
      offsetY: 1.2, // 相对于marker的Y轴偏移（单位：marker大小的倍数）
      offsetX: 0,   // 相对于marker的X轴偏移
      offsetZ: 0,   // 相对于marker的Z轴偏移
      name: `TextLabel_${Date.now()}`,
      // 文字样式
      textAlign: "center",
      textBaseline: "middle",
      maxWidth: 200, // 最大宽度，超出会自动换行
      lineHeight: 1.2,
    };

    const config = { ...defaultOptions, ...options };

    // 创建canvas来绘制文字
    const canvas = this._createTextCanvas(config);
    
    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    
    // 创建材质
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true,
      opacity: config.opacity,
      alphaTest: 0.1,
    });

    // 创建精灵
    const sprite = new THREE.Sprite(material);
    
    // 设置位置（包含偏移）
    const offsetPosition = this._calculateOffsetPosition(position, config);
    sprite.position.copy(offsetPosition);
    
    // 设置缩放（根据canvas大小调整）
    const scale = this._calculateSpriteScale(canvas, config);
    sprite.scale.set(scale.x, scale.y, 1);
    
    // 设置名称和用户数据
    sprite.name = config.name;
    sprite.userData = {
      type: "textLabel",
      text: config.text,
      originalPosition: position.clone(),
      offset: {
        x: config.offsetX,
        y: config.offsetY,
        z: config.offsetZ
      },
      config: config
    };

    return sprite;
  }

  /**
   * 创建文字canvas
   * @private
   * @param {Object} config - 配置对象
   * @returns {HTMLCanvasElement} canvas元素
   */
  static _createTextCanvas(config) {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    
    // 设置字体
    const fontSize = config.fontSize;
    context.font = `${fontSize}px ${config.fontFamily}`;
    
    // 处理文字换行
    const lines = this._wrapText(context, config.text, config.maxWidth);
    
    // 计算canvas尺寸
    const lineHeight = fontSize * config.lineHeight;
    const textWidth = Math.max(...lines.map(line => context.measureText(line).width));
    const textHeight = lines.length * lineHeight;
    
    const canvasWidth = textWidth + config.padding * 2;
    const canvasHeight = textHeight + config.padding * 2;
    
    // 设置canvas尺寸（使用2倍分辨率以提高清晰度）
    const pixelRatio = 2;
    canvas.width = canvasWidth * pixelRatio;
    canvas.height = canvasHeight * pixelRatio;
    canvas.style.width = canvasWidth + "px";
    canvas.style.height = canvasHeight + "px";
    
    // 重新设置字体（因为canvas尺寸改变了）
    context.scale(pixelRatio, pixelRatio);
    context.font = `${fontSize}px ${config.fontFamily}`;
    context.textAlign = config.textAlign;
    context.textBaseline = config.textBaseline;
    
    // 绘制背景
    if (config.backgroundColor && config.backgroundColor !== "transparent") {
      context.fillStyle = config.backgroundColor;
      context.fillRect(0, 0, canvasWidth, canvasHeight);
    }
    
    // 绘制边框
    if (config.borderWidth > 0 && config.borderColor) {
      context.strokeStyle = config.borderColor;
      context.lineWidth = config.borderWidth;
      context.strokeRect(0, 0, canvasWidth, canvasHeight);
    }
    
    // 绘制文字
    context.fillStyle = config.color;
    const startY = config.padding + (config.textBaseline === "middle" ? lineHeight / 2 : 0);
    
    lines.forEach((line, index) => {
      const x = canvasWidth / 2; // 居中对齐
      const y = startY + index * lineHeight;
      context.fillText(line, x, y);
    });
    
    return canvas;
  }

  /**
   * 文字换行处理
   * @private
   * @param {CanvasRenderingContext2D} context - canvas上下文
   * @param {string} text - 文字内容
   * @param {number} maxWidth - 最大宽度
   * @returns {Array<string>} 换行后的文字数组
   */
  static _wrapText(context, text, maxWidth) {
    const words = text.split("");
    const lines = [];
    let currentLine = "";

    for (let i = 0; i < words.length; i++) {
      const testLine = currentLine + words[i];
      const metrics = context.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && currentLine !== "") {
        lines.push(currentLine);
        currentLine = words[i];
      } else {
        currentLine = testLine;
      }
    }
    
    if (currentLine) {
      lines.push(currentLine);
    }
    
    return lines.length > 0 ? lines : [text];
  }

  /**
   * 计算偏移位置
   * @private
   * @param {THREE.Vector3} basePosition - 基础位置
   * @param {Object} config - 配置对象
   * @returns {THREE.Vector3} 偏移后的位置
   */
  static _calculateOffsetPosition(basePosition, config) {
    const offsetPosition = basePosition.clone();
    
    // 计算从地球中心指向位置的方向向量
    const direction = basePosition.clone().normalize();
    
    // Y轴偏移（沿着从地球中心向外的方向）
    if (config.offsetY !== 0) {
      offsetPosition.add(direction.clone().multiplyScalar(config.offsetY));
    }
    
    // X和Z轴偏移（在切平面上）
    if (config.offsetX !== 0 || config.offsetZ !== 0) {
      // 计算切平面的两个正交向量
      const up = new THREE.Vector3(0, 1, 0);
      const right = new THREE.Vector3().crossVectors(direction, up).normalize();
      const forward = new THREE.Vector3().crossVectors(right, direction).normalize();
      
      offsetPosition.add(right.multiplyScalar(config.offsetX));
      offsetPosition.add(forward.multiplyScalar(config.offsetZ));
    }
    
    return offsetPosition;
  }

  /**
   * 计算精灵缩放
   * @private
   * @param {HTMLCanvasElement} canvas - canvas元素
   * @param {Object} config - 配置对象
   * @returns {Object} 缩放值 {x, y}
   */
  static _calculateSpriteScale(canvas, config) {
    // 基础缩放因子（可以根据需要调整）
    const baseScale = 0.01;
    
    return {
      x: canvas.width * baseScale,
      y: canvas.height * baseScale
    };
  }

  /**
   * 更新文字标签内容
   * @param {THREE.Sprite} sprite - 文字标签精灵
   * @param {string} newText - 新的文字内容
   * @param {Object} options - 可选的配置更新
   */
  static updateTextLabel(sprite, newText, options = {}) {
    if (!sprite || !sprite.userData || sprite.userData.type !== "textLabel") {
      console.warn("TextLabel: 无效的文字标签对象");
      return;
    }

    // 更新配置
    const config = { ...sprite.userData.config, ...options, text: newText };
    sprite.userData.config = config;
    sprite.userData.text = newText;

    // 重新创建canvas和纹理
    const canvas = this._createTextCanvas(config);
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // 更新材质
    sprite.material.map.dispose(); // 释放旧纹理
    sprite.material.map = texture;
    sprite.material.needsUpdate = true;

    // 更新缩放
    const scale = this._calculateSpriteScale(canvas, config);
    sprite.scale.set(scale.x, scale.y, 1);
  }

  /**
   * 批量创建文字标签
   * @param {Array} labelData - 标签数据数组 [{position, text, ...options}]
   * @param {Object} commonOptions - 通用配置选项
   * @returns {Array<THREE.Sprite>} 文字标签数组
   */
  static createMultipleTextLabels(labelData, commonOptions = {}) {
    const labels = [];

    labelData.forEach((data, index) => {
      const options = {
        ...commonOptions,
        ...data,
        name: `TextLabel_${index}_${Date.now()}`
      };

      const label = this.createTextLabel(data.position, options);
      labels.push(label);
    });

    console.log(`TextLabel: 已创建 ${labels.length} 个文字标签`);
    return labels;
  }

  /**
   * 销毁文字标签
   * @param {THREE.Sprite} sprite - 文字标签精灵
   */
  static disposeTextLabel(sprite) {
    if (sprite && sprite.material) {
      if (sprite.material.map) {
        sprite.material.map.dispose();
      }
      sprite.material.dispose();
    }
  }
}

export default TextLabel;
