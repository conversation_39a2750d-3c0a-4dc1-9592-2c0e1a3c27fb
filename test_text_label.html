<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字标签测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #000;
            color: #fff;
        }
        #container {
            width: 800px;
            height: 600px;
            border: 1px solid #333;
            margin: 20px auto;
        }
        .info {
            text-align: center;
            margin: 20px;
        }
        .controls {
            text-align: center;
            margin: 20px;
        }
        button {
            margin: 5px;
            padding: 10px 20px;
            background-color: #333;
            color: #fff;
            border: 1px solid #666;
            cursor: pointer;
        }
        button:hover {
            background-color: #555;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>Three.js 文字标签测试</h1>
        <p>测试在3D场景中创建文字标签功能</p>
    </div>
    
    <div id="container"></div>
    
    <div class="controls">
        <button onclick="addTestMarker()">添加测试标记</button>
        <button onclick="addMarkerWithLabel()">添加带标签的标记</button>
        <button onclick="clearMarkers()">清除所有标记</button>
    </div>

    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.178.0/build/three.module.js';
        import { OrbitControls } from 'https://unpkg.com/three@0.178.0/examples/jsm/controls/OrbitControls.js';
        
        // 简化的TextLabel类用于测试
        class TextLabel {
            static createTextLabel(position, options = {}) {
                const defaultOptions = {
                    text: "Label",
                    fontSize: 16,
                    fontFamily: "Arial, sans-serif",
                    color: "#ffffff",
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    borderColor: "#ffffff",
                    borderWidth: 1,
                    padding: 8,
                    opacity: 1.0,
                    offsetY: 1.2,
                    offsetX: 0,
                    offsetZ: 0,
                    name: `TextLabel_${Date.now()}`,
                };

                const config = { ...defaultOptions, ...options };
                const canvas = this._createTextCanvas(config);
                const texture = new THREE.CanvasTexture(canvas);
                texture.needsUpdate = true;
                
                const material = new THREE.SpriteMaterial({
                    map: texture,
                    transparent: true,
                    opacity: config.opacity,
                    alphaTest: 0.1,
                });

                const sprite = new THREE.Sprite(material);
                const offsetPosition = this._calculateOffsetPosition(position, config);
                sprite.position.copy(offsetPosition);
                
                const scale = this._calculateSpriteScale(canvas, config);
                sprite.scale.set(scale.x, scale.y, 1);
                sprite.name = config.name;

                return sprite;
            }

            static _createTextCanvas(config) {
                const canvas = document.createElement("canvas");
                const context = canvas.getContext("2d");
                
                const fontSize = config.fontSize;
                context.font = `${fontSize}px ${config.fontFamily}`;
                
                const textWidth = context.measureText(config.text).width;
                const textHeight = fontSize;
                
                const canvasWidth = textWidth + config.padding * 2;
                const canvasHeight = textHeight + config.padding * 2;
                
                const pixelRatio = 2;
                canvas.width = canvasWidth * pixelRatio;
                canvas.height = canvasHeight * pixelRatio;
                
                context.scale(pixelRatio, pixelRatio);
                context.font = `${fontSize}px ${config.fontFamily}`;
                context.textAlign = "center";
                context.textBaseline = "middle";
                
                if (config.backgroundColor && config.backgroundColor !== "transparent") {
                    context.fillStyle = config.backgroundColor;
                    context.fillRect(0, 0, canvasWidth, canvasHeight);
                }
                
                if (config.borderWidth > 0 && config.borderColor) {
                    context.strokeStyle = config.borderColor;
                    context.lineWidth = config.borderWidth;
                    context.strokeRect(0, 0, canvasWidth, canvasHeight);
                }
                
                context.fillStyle = config.color;
                const x = canvasWidth / 2;
                const y = canvasHeight / 2;
                context.fillText(config.text, x, y);
                
                return canvas;
            }

            static _calculateOffsetPosition(basePosition, config) {
                const offsetPosition = basePosition.clone();
                const direction = basePosition.clone().normalize();
                
                if (config.offsetY !== 0) {
                    offsetPosition.add(direction.clone().multiplyScalar(config.offsetY));
                }
                
                return offsetPosition;
            }

            static _calculateSpriteScale(canvas, config) {
                const baseScale = 0.01;
                return {
                    x: canvas.width * baseScale,
                    y: canvas.height * baseScale
                };
            }
        }

        // 场景设置
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, 800/600, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(800, 600);
        renderer.setClearColor(0x000011);
        document.getElementById('container').appendChild(renderer.domElement);

        // 控制器
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;

        // 创建一个简单的地球
        const earthGeometry = new THREE.SphereGeometry(5, 32, 32);
        const earthMaterial = new THREE.MeshBasicMaterial({ 
            color: 0x4444ff, 
            wireframe: true,
            transparent: true,
            opacity: 0.3
        });
        const earth = new THREE.Mesh(earthGeometry, earthMaterial);
        scene.add(earth);

        // 设置相机位置
        camera.position.set(0, 0, 15);
        controls.update();

        // 存储标记的数组
        const markers = [];

        // 全局函数
        window.addTestMarker = function() {
            // 在地球表面随机位置创建标记
            const phi = Math.random() * Math.PI * 2;
            const theta = Math.random() * Math.PI;
            const radius = 5.1;
            
            const x = radius * Math.sin(theta) * Math.cos(phi);
            const y = radius * Math.cos(theta);
            const z = radius * Math.sin(theta) * Math.sin(phi);
            
            const position = new THREE.Vector3(x, y, z);
            
            // 创建简单的标记
            const markerGeometry = new THREE.SphereGeometry(0.1, 8, 8);
            const markerMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
            const marker = new THREE.Mesh(markerGeometry, markerMaterial);
            marker.position.copy(position);
            
            scene.add(marker);
            markers.push(marker);
            
            console.log('添加了测试标记');
        };

        window.addMarkerWithLabel = function() {
            // 在地球表面随机位置创建带标签的标记
            const phi = Math.random() * Math.PI * 2;
            const theta = Math.random() * Math.PI;
            const radius = 5.1;
            
            const x = radius * Math.sin(theta) * Math.cos(phi);
            const y = radius * Math.cos(theta);
            const z = radius * Math.sin(theta) * Math.sin(phi);
            
            const position = new THREE.Vector3(x, y, z);
            
            // 创建标记
            const markerGeometry = new THREE.SphereGeometry(0.1, 8, 8);
            const markerMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
            const marker = new THREE.Mesh(markerGeometry, markerMaterial);
            marker.position.copy(position);
            
            // 创建文字标签
            const countries = ['美国', '中国', '日本', '德国', '英国', '法国'];
            const randomCountry = countries[Math.floor(Math.random() * countries.length)];
            
            const label = TextLabel.createTextLabel(position, {
                text: randomCountry,
                fontSize: 14,
                color: "#ffffff",
                backgroundColor: "rgba(0, 255, 0, 0.8)",
                borderColor: "#ffffff",
                borderWidth: 1,
                padding: 6,
                offsetY: 1.5,
            });
            
            scene.add(marker);
            scene.add(label);
            markers.push(marker, label);
            
            console.log(`添加了带标签的标记: ${randomCountry}`);
        };

        window.clearMarkers = function() {
            markers.forEach(marker => {
                scene.remove(marker);
                if (marker.material) {
                    marker.material.dispose();
                }
                if (marker.geometry) {
                    marker.geometry.dispose();
                }
            });
            markers.length = 0;
            console.log('清除了所有标记');
        };

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            earth.rotation.y += 0.005;
            renderer.render(scene, camera);
        }
        animate();

        console.log('文字标签测试页面已加载');
    </script>
</body>
</html>
