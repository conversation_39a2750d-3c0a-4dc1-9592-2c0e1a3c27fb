<template>
  <div class="wrapper">
    <div class="scene">
      <div class="globe" ref="containerRef" style="width: 100%; height: 100%">
        <!-- Scene is now created imperatively in onMounted, not as a Vue component -->
      </div>
    </div>
    <Home />

    <transition name="fade-out">
      <div v-if="showLoading" class="loading">
        <Loading :progress="loadingProgress" />
      </div>
    </transition>

    <!-- <div class="controls">
      <Controls :time="state.realTime + state.offset" :prevOffsetMsRef="prevOffsetMsRef" @change-offset="handleChangeOffset" />
    </div> -->

    <div class="tc" :style="tcPopupStyle">
      <!-- <div class="tc" :style="tcPopupStyle" v-show="tcPopup.visible"> -->
      <div class="tc-content">
        <div class="tc-title">{{ dataStore.LineTcData.alarmType || "恶意机器人攻击" }}</div>
        <div class="tc-info">
          <div class="tc-item">
            <span class="tc-label">攻击时间：</span>
            <span class="tc-value">{{ dataStore.LineTcData.attackTime || "2025-07-04 13:47:34" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">风险等级：</span>
            <span class="tc-value" :class="getRiskLevelClass(dataStore.LineTcData.hazardRating)">
              {{ dataStore.LineTcData.hazardRating || "高危" }}
            </span>
          </div>
          <div class="tc-item">
            <span class="tc-label">攻击来源：</span>
            <span class="tc-value">{{ dataStore.LineTcData.attackCountry || "美国" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">攻击IP：</span>
            <span class="tc-value">{{ dataStore.LineTcData.attackSip || "**************" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">被攻击IP：</span>
            <span class="tc-value">{{ dataStore.LineTcData.attackDip || "**************" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">被攻击端口：</span>
            <span class="tc-value">{{ dataStore.LineTcData.attackDport || "80" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">攻击端口：</span>
            <span class="tc-value">{{ dataStore.LineTcData.attackSport || "25578" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">攻击状态：</span>
            <span class="tc-value">{{ dataStore.LineTcData.attackStatus || "无" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">动作：</span>
            <span class="tc-value">{{ dataStore.LineTcData.action || "允许" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">采集设备：</span>
            <span class="tc-value">{{ dataStore.LineTcData.collectDevice || "QiAnXin(奇安信)SkyEye prom" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">采集设备IP：</span>
            <span class="tc-value">{{ dataStore.LineTcData.collectDeviceIp || "**************" }}</span>
          </div>
          <div class="tc-item">
            <span class="tc-label">规则描述：</span>
            <span class="tc-value">
              {{ dataStore.LineTcData.ruleDescription }}
            </span>
          </div>
        </div>
        <button class="tc-close-btn" @click="closeTcPopup">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, provide } from "vue";
import Controls from "./components/Controls.vue";
import { getSunCoords } from "./utils";
import { Scene } from "./components/three/models/Scene/Scene.js";
import autofit from "autofit.js";
import Home from "./views/Home.vue";
import { usedata } from "./store/data";

import Loading from "./views/Loading.vue";

// 初始化数据
const dataStore = usedata();
dataStore.getData();
// 初始化 WebSocket 连接
dataStore.getWss();

const state = reactive({
  realTime: Date.now(),
  offset: 0,
});

const sunCoordsRef = ref({ lat: 0, lng: 0 });
const prevOffsetMsRef = ref({ current: 0 });
const containerRef = ref(null);

// UI动画控制状态
const uiAnimationTriggered = ref(false);

// 加载状态控制
const showLoading = ref(true);
const loadingProgress = ref(0);

// tc弹窗状态
const tcPopup = reactive({
  visible: false,
  screenPosition: { x: 0, y: 0 },
});

// 计算tc弹窗的样式
const tcPopupStyle = computed(() => {
  if (!tcPopup.visible) {
    return {};
  }

  const { x, y } = tcPopup.screenPosition;

  // 确保弹窗不会超出屏幕边界
  const popupWidth = 320;
  const popupHeight = 300;
  const margin = 20;

  let left = x - popupWidth / 2;
  let top = y - popupHeight / 2;

  // 边界检查
  if (left < margin) left = margin;
  if (left + popupWidth > window.innerWidth - margin) {
    left = window.innerWidth - popupWidth - margin;
  }

  if (top < margin) top = margin;
  if (top + popupHeight > window.innerHeight - margin) {
    top = window.innerHeight - popupHeight - margin;
  }

  return {
    position: "fixed",
    left: `${left}px`,
    top: `${top}px`,
    zIndex: 10000,
    transform: "none", // 覆盖原有的transform
  };
});

// 提供给子组件的UI动画触发状态
provide("uiAnimationTriggered", uiAnimationTriggered);

// Initialize the scene when component mounts
onMounted(async () => {
  autofit.init();
  try {
    // Initialize sun position before creating the scene
    getSunPosition();

    // Make sure we have the DOM element
    if (containerRef.value) {
      // Create Scene singleton instance
      const scene = Scene.createInstance(containerRef.value, {
        sunCoordsRef: sunCoordsRef,
        onLoad: handleLoad,
      });

      // 监听纹理加载进度事件
      if (scene) {
        // 监听纹理加载进度
        scene.addEventListener("textureLoadProgress", (data) => {
          console.log("纹理加载进度:", data.progress.toFixed(1) + "%");
          loadingProgress.value = data.progress;
        });

        // 监听纹理加载完成
        scene.addEventListener("textureLoadComplete", (data) => {
          console.log("纹理加载完成，隐藏Loading界面");
          // 延迟一点时间再隐藏Loading，让用户看到100%的进度
          setTimeout(() => {
            showLoading.value = false;
          }, 500);
        });

        // 监听纹理加载错误
        scene.addEventListener("textureLoadError", (data) => {
          console.error("纹理加载失败:", data.url);
          // 即使加载失败也隐藏Loading界面
          setTimeout(() => {
            showLoading.value = false;
          }, 1000);
        });

        // 监听UI动画触发事件
        scene.addEventListener("uiAnimationTrigger", (data) => {
          console.log("UI动画触发事件接收到:", data);
          uiAnimationTriggered.value = true;
        });

        // 监听飞线点击事件
        scene.addEventListener("flightLineClicked", (data) => {
          console.log("飞线点击事件接收到:", data);

          // 只有当点击位置在屏幕可见范围内时才显示弹窗
          if (data.screenPosition.isVisible) {
            // 设置飞线数据到 store
            dataStore.setLineTcData(data.flightLineData);

            tcPopup.visible = true;
            tcPopup.screenPosition = data.screenPosition;
            // 显示弹窗时暂停自动旋转并通知 Scene 弹窗状态
            scene.pauseAutoRotation();
            scene.setPopupVisible(true);
          } else {
            console.log("点击位置不在可见范围内，不显示弹窗");
          }
        });

        // 监听用户交互事件（用于隐藏弹窗）
        scene.addEventListener("userInteractionStart", (data) => {
          console.log("用户交互开始，隐藏弹窗");
          if (tcPopup.visible) {
            closeTcPopup();
          }
        });
      }
    }
  } catch (error) {
    console.error("Failed to load Scene:", error);
  }
});

// Cleanup function to destroy the scene when component unmounts
onUnmounted(() => {
  dataStore.closeWss();
  const scene = Scene.getInstance();
  if (scene) {
    scene.destroy();
  }
});

const handleChangeOffset = (offset) => {
  state.offset = offset;
  getSunPosition();
};

// 关闭tc弹窗
const closeTcPopup = () => {
  tcPopup.visible = false;
  tcPopup.screenPosition = { x: 0, y: 0 };

  // 清空飞线数据
  dataStore.clearLineTcData();

  // 重置所有飞线状态
  const scene = Scene.getInstance();
  if (scene) {
    scene.resetAllFlightLineStates();

    // 通知 Scene 弹窗已关闭
    scene.setPopupVisible(false);

    // 弹窗关闭后，触发恢复到默认状态，然后重新开启自动旋转
    if (scene.controls) {
      // 先恢复自动旋转
      scene.resumeAutoRotation();

      // 然后触发恢复到默认状态（这会在空闲时自动触发）
      // 手动重置空闲状态，让系统在3秒后自动恢复到默认状态并开启自动旋转
      scene.controls.resetIdleState();
    }
  }
};

// 根据风险等级获取样式类
const getRiskLevelClass = (hazardRating) => {
  if (!hazardRating) return "";

  const rating = hazardRating.toLowerCase();
  if (rating.includes("高") || rating.includes("high") || rating.includes("严重")) {
    return "tc-risk-high";
  } else if (rating.includes("中") || rating.includes("medium")) {
    return "tc-risk-medium";
  } else if (rating.includes("低") || rating.includes("low")) {
    return "tc-risk-low";
  }
  return "";
};

const getSunPosition = () => {
  // return;

  const coords = getSunCoords(new Date(state.realTime + state.offset));
  console.log("🚀 ~ coords:", coords);
  sunCoordsRef.value = { lat: coords.lat, lng: coords.lng };
  // sunCoordsRef.value = { lat: 21, lng: 158 };
  sunCoordsRef.value = { lat: 18, lng: -152 };
};

const handleLoad = () => {
  getSunPosition();
};
</script>

<style scoped>
.wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.scene {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.globe {
  width: 100%;
  height: 100%;
}

.controls {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  height: 100px;
  z-index: 100;
}
/* 淡出动画样式 */
.fade-out-enter-active,
.fade-out-leave-active {
  transition: opacity 0.8s ease-out;
}

.fade-out-enter-from,
.fade-out-leave-to {
  opacity: 0;
}

.loading {
  position: absolute;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 666;
  background-color: rgb(0, 0, 0);
}
.tc {
  position: absolute;
  top: 50px;
  left: 50px;
  width: 302px;
  /* height: 441.14px; */
  background-image: url("../public/img/tt.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 100;
}

.tc-content {
  padding: 15px 25px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}

.tc-title {
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 12px;
  text-align: left;
}

.tc-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
}

.tc-item {
  display: flex;
  align-items: flex-start;
  line-height: 1.4;
}

.tc-label {
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 14px;
  color: #a0a0a0;
  flex-shrink: 0;
  margin-right: 8px;
}

.tc-value {
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
  word-wrap: break-word;
  word-break: break-all;
  flex: 1;
}

.tc-risk-high {
  color: #ff614d;
  font-weight: 500;
}

.tc-risk-medium {
  color: #ff9145;
  font-weight: 500;
}

.tc-risk-low {
  color: #1bfefd;
  font-weight: 500;
}

.tc-close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  color: #ffffff;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.tc-close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  opacity: 1;
  transform: scale(1.1);
}
</style>
